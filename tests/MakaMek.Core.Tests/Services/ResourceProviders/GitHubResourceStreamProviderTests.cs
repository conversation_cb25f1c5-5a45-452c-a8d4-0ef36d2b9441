using System.Net;
using System.Text;
using NSubstitute;
using Sanet.MakaMek.Core.Services;
using Sanet.MakaMek.Core.Services.ResourceProviders;
using Shouldly;

namespace Sanet.MakaMek.Core.Tests.Services.ResourceProviders;

public class MockHttpMessageHandler : HttpMessageHandler
{
    public string? ResponseContent { get; set; }
    public HttpStatusCode StatusCode { get; set; } = HttpStatusCode.OK;
    public bool IsDisposed { get; private set; }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        await Task.CompletedTask; // Ensure async behavior

        if (StatusCode != HttpStatusCode.OK)
        {
            return new HttpResponseMessage
            {
                StatusCode = StatusCode
            };
        }

        var content = ResponseContent ?? string.Empty;
        return new HttpResponseMessage
        {
            StatusCode = HttpStatusCode.OK,
            Content = new StringContent(content, Encoding.UTF8, "application/octet-stream")
        };
    }

    protected override void Dispose(bool disposing)
    {
        IsDisposed = true;
        base.Dispose(disposing);
    }
}

public class ThrowingHttpMessageHandler : HttpMessageHandler
{
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        throw new HttpRequestException("Network error");
    }
}

public class ExceptionThrowingHttpMessageHandler : HttpMessageHandler
{
    protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
    {
        throw new InvalidOperationException("Some other error");
    }
}

public class GitHubResourceStreamProviderTests
{
    private readonly MockHttpMessageHandler _mockHttpMessageHandler;
    private readonly GitHubResourceStreamProvider _sut;

    public GitHubResourceStreamProviderTests()
    {
        _mockHttpMessageHandler = new MockHttpMessageHandler
        {
            StatusCode = HttpStatusCode.OK
        };

        var httpClient = new HttpClient(_mockHttpMessageHandler)
        {
            BaseAddress = new Uri("https://api.github.com/")
        };

        _sut = new GitHubResourceStreamProvider("mmux", "https://api.github.com/test", httpClient);
    }

    [Fact]
    public void Constructor_ShouldCreateHttpClient_WhenNotProvided()
    {
        // Arrange & Act
        var sut = new GitHubResourceStreamProvider("ext", "url");

        // Assert - Should not throw and should be able to get resource IDs
        sut.ShouldNotBeNull();
        sut.Dispose();
    }

    [Fact]
    public async Task GetResourceStream_ShouldReturnNull_WhenResourceIdIsEmpty()
    {
        // Arrange & Act
        var result = await _sut.GetResourceStream("");

        // Assert
        result.ShouldBeNull();
    }

    [Fact]
    public async Task GetResourceStream_ShouldReturnNull_WhenResourceIdIsNull()
    {
        // Arrange & Act
        var result = await _sut.GetResourceStream(null!);

        // Assert
        result.ShouldBeNull();
    }

    [Fact]
    public async Task GetResourceStream_ShouldReturnStream_WhenValidResourceIdProvided()
    {
        // Arrange
        const string testContent = "test file content";
        _mockHttpMessageHandler.ResponseContent = testContent;

        // Act
        var result = await _sut.GetResourceStream("https://api.github.com/test/file.mmux");

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeOfType<MemoryStream>();

        using var reader = new StreamReader(result);
        var content = await reader.ReadToEndAsync();
        content.ShouldBe(testContent);
    }

    [Fact]
    public async Task GetResourceStream_ShouldReturnNull_WhenHttpRequestFails()
    {
        // Arrange
        _mockHttpMessageHandler.StatusCode = HttpStatusCode.NotFound;

        // Act
        var result = await _sut.GetResourceStream("https://api.github.com/test/file.mmux");

        // Assert
        result.ShouldBeNull();
    }

    [Fact]
    public void Dispose_ShouldNotDisposeExternallyProvidedHttpClient()
    {
        // Arrange
        var mockHttpMessageHandler = new MockHttpMessageHandler();
        var externalHttpClient = new HttpClient(mockHttpMessageHandler);

        var provider = new GitHubResourceStreamProvider("mmux", "https://api.github.com/test", externalHttpClient);

        // Act
        provider.Dispose();

        // Assert - External HttpClient should not be disposed
        mockHttpMessageHandler.IsDisposed.ShouldBeFalse();
    }

    [Fact]
    public async Task GetAvailableResourceIds_ShouldReturnFiles_WhenValidGitHubResponseProvided()
    {
        // Arrange
        const string jsonResponse = """
                                    [
                                        {
                                            "name": "file1.mmux",
                                            "type": "file",
                                            "download_url": "https://raw.githubusercontent.com/test/file1.mmux"
                                        },
                                        {
                                            "name": "file2.mmux",
                                            "type": "file",
                                            "download_url": "https://raw.githubusercontent.com/test/file2.mmux"
                                        },
                                        {
                                            "name": "directory",
                                            "type": "dir",
                                            "download_url": null
                                        },
                                        {
                                            "name": "file.txt",
                                            "type": "file",
                                            "download_url": "https://raw.githubusercontent.com/test/file.txt"
                                        }
                                    ]
                                    """;

        _mockHttpMessageHandler.ResponseContent = jsonResponse;

        // Act
        var result = (await _sut.GetAvailableResourceIds()).ToList();

        // Assert
        result.ShouldNotBeNull();
        result.Count.ShouldBe(2);
        result.ShouldContain("https://raw.githubusercontent.com/test/file1.mmux");
        result.ShouldContain("https://raw.githubusercontent.com/test/file2.mmux");
    }

    [Fact]
    public async Task GetAvailableResourceIds_ShouldReturnEmptyList_WhenGitHubResponseIsEmpty()
    {
        // Arrange
        _mockHttpMessageHandler.ResponseContent = "[]";

        // Act
        var result = (await _sut.GetAvailableResourceIds()).ToList();

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task GetAvailableResourceIds_ShouldReturnEmptyList_WhenFilesHaveNoDownloadUrl()
    {
        // Arrange
        const string jsonResponse = """
                                    [
                                        {
                                            "name": "file1.mmux",
                                            "type": "file",
                                            "download_url": null
                                        },
                                        {
                                            "name": "file2.mmux",
                                            "type": "file",
                                            "download_url": null
                                        }
                                    ]
                                    """;

        _mockHttpMessageHandler.ResponseContent = jsonResponse;

        // Act
        var result = (await _sut.GetAvailableResourceIds()).ToList();

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task GetAvailableResourceIds_ShouldReturnEmptyList_WhenHttpStatusCodeIsError()
    {
        // Arrange
        _mockHttpMessageHandler.StatusCode = HttpStatusCode.InternalServerError;

        // Act
        var result = (await _sut.GetAvailableResourceIds()).ToList();

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task GetAvailableResourceIds_ShouldReturnEmptyList_WhenJsonParsingFails()
    {
        // Arrange
        _mockHttpMessageHandler.ResponseContent = "invalid json content";

        // Act
        var result = (await _sut.GetAvailableResourceIds()).ToList();

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task GetAvailableResourceIds_ShouldReturnEmptyList_WhenHttpRequestExceptionThrown()
    {
        // Arrange - Create a mock handler that throws HttpRequestException
        var exceptionHandler = new ThrowingHttpMessageHandler();
        var httpClient = new HttpClient(exceptionHandler)
        {
            BaseAddress = new Uri("https://api.github.com/")
        };

        var sut = new GitHubResourceStreamProvider("mmux", "https://api.github.com/test", httpClient);

        // Act
        var result = (await sut.GetAvailableResourceIds()).ToList();

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBeEmpty();
    }

    [Fact]
    public async Task GetResourceStream_ShouldReturnNull_WhenHttpRequestExceptionThrown()
    {
        // Arrange - Create a mock handler that throws HttpRequestException
        var exceptionHandler = new ThrowingHttpMessageHandler();
        var httpClient = new HttpClient(exceptionHandler)
        {
            BaseAddress = new Uri("https://api.github.com/")
        };

        var sut = new GitHubResourceStreamProvider("mmux", "https://api.github.com/test", httpClient);

        // Act
        var result = await sut.GetResourceStream("https://api.github.com/test/file.mmux");

        // Assert
        result.ShouldBeNull();
    }

    [Fact]
    public async Task GetResourceStream_ShouldReturnNull_WhenOtherExceptionThrown()
    {
        // Arrange - Create a mock handler that throws a general exception
        var exceptionHandler = new ExceptionThrowingHttpMessageHandler();
        var httpClient = new HttpClient(exceptionHandler)
        {
            BaseAddress = new Uri("https://api.github.com/")
        };

        var sut = new GitHubResourceStreamProvider("mmux", "https://api.github.com/test", httpClient);

        // Act
        var result = await sut.GetResourceStream("https://api.github.com/test/file.mmux");

        // Assert
        result.ShouldBeNull();
    }

    [Fact]
    public async Task GetResourceStream_ShouldUseCaching_WhenCachingServiceProvided()
    {
        // Arrange
        var testContent = "Test file content";
        var testUrl = "https://api.github.com/test/file.mmux";

        var mockHandler = new MockHttpMessageHandler
        {
            ResponseContent = testContent
        };
        var httpClient = new HttpClient(mockHandler);

        var mockCachingService = Substitute.For<IFileCachingService>();
        mockCachingService.TryGetCachedFile(testUrl).Returns((Stream?)null); // Not cached initially

        var sut = new GitHubResourceStreamProvider("mmux", "https://api.github.com/test", httpClient, mockCachingService);

        // Act
        var result = await sut.GetResourceStream(testUrl);

        // Assert
        result.ShouldNotBeNull();

        // Verify caching service was called
        await mockCachingService.Received(1).TryGetCachedFile(testUrl);

        // Give some time for the background caching task to complete
        await Task.Delay(100);
        await mockCachingService.Received(1).SaveToCache(testUrl, Arg.Any<Stream>());
    }

    [Fact]
    public async Task GetResourceStream_ShouldReturnCachedContent_WhenFileIsCached()
    {
        // Arrange
        var testContent = "Cached file content";
        var testUrl = "https://api.github.com/test/cached-file.mmux";
        var cachedStream = new MemoryStream(Encoding.UTF8.GetBytes(testContent));

        var mockHandler = new MockHttpMessageHandler(); // Should not be called
        var httpClient = new HttpClient(mockHandler);

        var mockCachingService = Substitute.For<IFileCachingService>();
        mockCachingService.TryGetCachedFile(testUrl).Returns(cachedStream);

        var sut = new GitHubResourceStreamProvider("mmux", "https://api.github.com/test", httpClient, mockCachingService);

        // Act
        var result = await sut.GetResourceStream(testUrl);

        // Assert
        result.ShouldNotBeNull();
        result.ShouldBe(cachedStream);

        // Verify HTTP client was not used
        mockHandler.ResponseContent.ShouldBeNull();

        // Verify caching service was called
        await mockCachingService.Received(1).TryGetCachedFile(testUrl);
        await mockCachingService.DidNotReceive().SaveToCache(Arg.Any<string>(), Arg.Any<Stream>());
    }

    [Fact]
    public async Task GetResourceStream_ShouldWorkWithoutCaching_WhenNoCachingServiceProvided()
    {
        // Arrange
        var testContent = "Test file content without caching";
        var testUrl = "https://api.github.com/test/file.mmux";

        var mockHandler = new MockHttpMessageHandler
        {
            ResponseContent = testContent
        };
        var httpClient = new HttpClient(mockHandler);

        var sut = new GitHubResourceStreamProvider("mmux", "https://api.github.com/test", httpClient, null);

        // Act
        var result = await sut.GetResourceStream(testUrl);

        // Assert
        result.ShouldNotBeNull();
        using var reader = new StreamReader(result);
        var content = await reader.ReadToEndAsync();
        content.ShouldBe(testContent);
    }

    [Fact]
    public async Task GetResourceStream_ShouldHandleCachingErrors_Gracefully()
    {
        // Arrange
        var testContent = "Test file content";
        var testUrl = "https://api.github.com/test/file.mmux";

        var mockHandler = new MockHttpMessageHandler
        {
            ResponseContent = testContent
        };
        var httpClient = new HttpClient(mockHandler);

        var mockCachingService = Substitute.For<IFileCachingService>();
        mockCachingService.TryGetCachedFile(testUrl).Returns((Stream?)null);
        mockCachingService.When(x => x.SaveToCache(Arg.Any<string>(), Arg.Any<Stream>()))
                         .Do(x => throw new InvalidOperationException("Cache error"));

        var sut = new GitHubResourceStreamProvider("mmux", "https://api.github.com/test", httpClient, mockCachingService);

        // Act
        var result = await sut.GetResourceStream(testUrl);

        // Assert - Should still return content despite caching error
        result.ShouldNotBeNull();
        using var reader = new StreamReader(result);
        var content = await reader.ReadToEndAsync();
        content.ShouldBe(testContent);
    }
}
